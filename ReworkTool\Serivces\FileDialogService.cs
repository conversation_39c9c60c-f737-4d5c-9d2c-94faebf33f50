using Microsoft.Win32;
using ReworkTool.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ReworkTool.Serivces
{
    public class FileDialogService:IFileDialogService
    {
        public bool OpenFileDialog(out string filePath,string filter="JSON文件|*.json|所有文件|*.*")
        {
            var dialog = new OpenFileDialog
            {
                Filter = filter,
                Title = "选择配置文件"
            };
            var result=dialog.ShowDialog();
            if (result==true)
            {
                filePath = dialog.FileName;
                return true;
            }
            filePath = null;
            return false;
        }
    }
}
