﻿using Newtonsoft.Json;
using Prism.Commands;
using Prism.Ioc;
using Prism.Mvvm;
using Prism.Regions;
using Prism.Services.Dialogs;
using ReworkTool.Interfaces;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace ReworkTool.ViewModels
{
    public class LoginViewModel :BindableBase,INavigationAware
    {
        private bool _isFileSelected;
        private string _selectedFileName;
        public DelegateCommand SelectFileCommand { get; private set; }
        public DelegateCommand ImportFileCommand { get; private set; }
        private IFileDialogService _fileDialogService;
        private readonly IRegionManager _regionManager;
        private readonly IContainerProvider _containerProvider;
        private readonly IDatabaseService _databaseService;
        public LoginViewModel(IFileDialogService fileDialogService,IRegionManager regionManager,IContainerProvider containerProvider,IDatabaseService databaseService)
        {
            _fileDialogService= fileDialogService;
            _regionManager = regionManager;
            _containerProvider= containerProvider;
            _databaseService= databaseService;
            SelectFileCommand = new DelegateCommand(SelectFile);
            ImportFileCommand = new DelegateCommand(ImportFile);
        }
        public bool IsFileSelected
        {
            get { return _isFileSelected; }
            set { SetProperty(ref _isFileSelected, value); }
        }
        public string SelectedFileName
        {
            get { return _selectedFileName; }
            set { SetProperty(ref _selectedFileName, value); }
        }
        private void SelectFile() 
        {
            if(_fileDialogService.OpenFileDialog(out string filePath,"JSON文件|*.json|所有文件|*.*"))
            {
                SelectedFileName=filePath;
                IsFileSelected = true;
            }
        }
        private void ImportFile()
        {
            try
            {
                string jsonContent = File.ReadAllText(SelectedFileName);
                AppConfig appConfig = JsonConvert.DeserializeObject<AppConfig>(jsonContent);
                
                if (appConfig != null)
                {
                    ClearRegion();
                    var mainWindow = Application.Current.MainWindow;
                    var mainWindowViewModel = mainWindow.DataContext as MainWindowViewModel;
                    mainWindowViewModel.DeviceCount = appConfig.DeviceList.Count;
                    for (int i = 0; i < appConfig.DeviceList.Count; i++)
                    {
                        var device = appConfig.DeviceList[i];
                        var parameters = new NavigationParameters();
                        parameters.Add("MysqlTable", appConfig.MysqlTable);
                        parameters.Add("Device", device);
                        parameters.Add("CommandList", appConfig.CommandList);

                        string uniqueViewName = $"TestView?id={i}";
                        _regionManager.RequestNavigate("ContentRegion", uniqueViewName, parameters);
                    }
                    _databaseService.CreateTable(appConfig.MysqlTable, appConfig.CommandList.Select(c => c.FieldName).ToList());
                }
                else
                {
                    MessageBox.Show("配置文件为空");
                }
            }
            catch (Exception ex) 
            {
                MessageBox.Show($"解析配置文件失败{ex}");
            }
        }
        private void ClearRegion()
        {
            var region = _regionManager.Regions["ContentRegion"];
            region.RemoveAll();
        }
        public void OnNavigatedTo(NavigationContext navigationContext)
        {
            var mainWindow = Application.Current.MainWindow;
            mainWindow.Width = 600;
            mainWindow.Height=400;
        }

        public bool IsNavigationTarget(NavigationContext navigationContext)
        {
            return true;
        }

        public void OnNavigatedFrom(NavigationContext navigationContext)
        {
            
        }
    }
}
