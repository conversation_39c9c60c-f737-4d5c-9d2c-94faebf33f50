﻿using Prism.Mvvm;
using Prism.Regions;
using System.Windows.Threading;
using System.Windows.Threading;

namespace ReworkTool.ViewModels
{
    public class MainWindowViewModel : BindableBase
    {
        private string _title = "通用返修";
        private int _deviceCount = 1;
        private readonly IRegionManager _regionManager;
        
        public string Title
        {
            get { return _title; }
            set { SetProperty(ref _title, value); }
        }
        public int DeviceCount
        {
            get { return _deviceCount; }
            set { SetProperty(ref _deviceCount, value); }
        }
        public MainWindowViewModel(IRegionManager regionManager)
        {
            _regionManager = regionManager;

            Dispatcher.CurrentDispatcher.BeginInvoke(new System.Action(() =>
            {
                ChangeRegionContent("LoginView");
            }));
        }
        
        private void ChangeRegionContent(string viewName)
        {
            try
            {
                _regionManager.RequestNavigate("ContentRegion", viewName);
            }
            catch (System.Exception ex)
            {
                System.Windows.MessageBox.Show($"导航失败: {ex.Message}");
            }
        }
    }
}
