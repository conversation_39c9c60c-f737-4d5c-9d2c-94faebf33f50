﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Data;

namespace ReworkTool.Converters
{
    public class BooleanToVisibilityConverter: IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if(value is not bool boolValue)
            {
                return Visibility.Collapsed;
            }
            bool invert = parameter is string str &&
                (str.Equals("Inverted", StringComparison.OrdinalIgnoreCase) ||
                          str.Equals("True", StringComparison.OrdinalIgnoreCase) ||
                          str.Equals("1"));
            return (invert? !boolValue:boolValue)?Visibility.Visible:Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
           return null;
        }
    }
}
