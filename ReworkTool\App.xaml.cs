﻿using System.Windows;
using Prism.Ioc;
using ReworkTool.ViewModels;
using ReworkTool.Views;
using ReworkTool.Interfaces;
using ReworkTool.Serivces;

namespace ReworkTool
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App
    {
        protected override Window CreateShell()
        {
            return Container.Resolve<MainWindow>();
        }

        protected override void RegisterTypes(IContainerRegistry containerRegistry)
        {
            // 注册服务
            containerRegistry.RegisterSingleton<IFileDialogService, FileDialogService>();
            containerRegistry.RegisterSingleton<IDatabaseService, DatabaseService>();
            containerRegistry.Register<ISerialPortService, SerialPortService>();
            // 注册导航
            containerRegistry.RegisterForNavigation<LoginView, LoginViewModel>();
            containerRegistry.RegisterForNavigation<TestView, TestViewModel>();
        }
    }
}
