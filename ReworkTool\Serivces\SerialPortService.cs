﻿using ReworkTool.Interfaces;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace ReworkTool.Serivces
{
    public class SerialPortService:ISerialPortService
    {
        private SerialPort _serialPort;
        private readonly object _lockObject = new object();
        private StringBuilder _dataBuffer = new StringBuilder();

        public bool IsConnected => _serialPort != null && _serialPort.IsOpen;
        public string CurrentPort => IsConnected ? _serialPort.PortName : string.Empty;

        public event EventHandler<string> DataReceived;
        public event EventHandler<Exception> ErrorOccurred;

        //连接串口
        public async Task<bool> ConnectAsync(string portName, int baudRate = 9600)
        {
            return await Task.Run(() =>
            {
                try
                {
                    // 强制断开现有连接(处理设备被拔出的情况)
                    if (_serialPort != null)
                    {
                        try { _serialPort.DataReceived -= SerialPort_DataReceived; } catch { }
                        try { if (_serialPort.IsOpen) _serialPort.Close(); } catch { }
                        try { _serialPort.Dispose(); } catch { }
                        _serialPort = null;
                        GC.Collect();
                        Thread.Sleep(300); // 等待系统释放
                    }
                    // 如果已经连接到相同的端口，直接返回成功
                    if (IsConnected && _serialPort.PortName == portName)
                    {
                        return true;
                    }

                    // 如果已经连接到其他端口，先断开
                    if (IsConnected)
                    {
                        _serialPort.DataReceived -= SerialPort_DataReceived;
                        _serialPort.Close();
                        _serialPort.Dispose();
                        _serialPort = null;
                    }

                    // 创建新的串口连接
                    _serialPort = new SerialPort
                    {
                        PortName = portName,
                        BaudRate = baudRate,
                        DataBits = 8,
                        Parity = Parity.None,
                        StopBits = StopBits.One,
                        ReadTimeout = 1000,
                        WriteTimeout = 1000
                    };

                    // 清空缓冲区
                    _dataBuffer.Clear();

                    // 打开串口
                    _serialPort.Open();

                    // 注册数据接收事件
                    _serialPort.DataReceived += SerialPort_DataReceived;
                    return true;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"连接异常详情: {ex.GetType().Name}");
                    Debug.WriteLine($"异常消息: {ex.Message}");
                    if (ex.InnerException != null)
                    {
                        Debug.WriteLine($"内部异常: {ex.InnerException.Message}");
                    }
                    Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                    return false;
                }
            });
        }

        //断开串口连接
        public Task<bool> DisconnectAsync()
        {
            return Task.Run(() =>
            {
                try
                {
                    if (_serialPort != null)
                    {
                        // 取消事件订阅 - 即使设备被拔出也要尝试
                        try { _serialPort.DataReceived -= SerialPort_DataReceived; } catch { }

                        // 尝试关闭 - 设备被拔出时可能失败
                        try
                        {
                            if (_serialPort.IsOpen) _serialPort.Close();
                            Debug.WriteLine("串口已关闭");
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"关闭串口失败(可能被拔出): {ex.Message}");
                        }

                        // 尝试释放 - 设备被拔出时可能失败
                        try
                        {
                            _serialPort.Dispose();
                            Debug.WriteLine("串口已释放");
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"释放串口失败(可能被拔出): {ex.Message}");
                        }
                    }

                    return true;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"断开异常: {ex.Message}");
                    return true; // 返回true，因为我们已经尽力清理了
                }
                finally
                {
                    // 无论如何都要清理引用
                    _serialPort = null;
                    _dataBuffer.Clear();

                    // 强制垃圾回收，处理设备被拔出的情况
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    Thread.Sleep(200);

                    Debug.WriteLine("串口对象已清空");
                }
            });
        }

        //发送命令
        public Task<bool> SendCommandAsync(string command)
        {
            return Task.Run(() =>
            {
                if (!IsConnected)
                {
                    OnErrorOccurred(new InvalidOperationException("串口未连接"));
                    return false;
                }

                try
                {
                    lock (_lockObject)
                    {
                        // 清空内部数据缓冲区，为新的响应做准备
                        _dataBuffer.Clear();

                        // 发送命令
                        _serialPort.Write(command);
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    OnErrorOccurred(ex);
                    return false;
                }
            });
        }

        //获取串口返回值
        public Task<string> ReceiveDataAsync(int timeout = 1000)
        {
            return Task.Run(() =>
            {
                if (!IsConnected) return string.Empty;

                try
                {
                    StringBuilder response = new StringBuilder();
                    DateTime startTime = DateTime.Now;
                    DateTime lastDataTime = DateTime.Now;
                    bool hasReceivedData = false;

                    while ((DateTime.Now - startTime).TotalMilliseconds < timeout)
                    {
                        lock (_lockObject)
                        {
                            // 检查是否有新数据到达
                            if (_serialPort.BytesToRead > 0)
                            {
                                string data = _serialPort.ReadExisting();
                                response.Append(data);
                                hasReceivedData = true;
                                lastDataTime = DateTime.Now;

                                Debug.WriteLine($"接收到数据: {data}");
                                Debug.WriteLine($"累计数据: {response.ToString()}");

                                // 检查是否收到完整响应
                                string current = response.ToString().ToUpper();
                                if (current.Contains("OK") || current.Contains("ERROR"))
                                {
                                    Debug.WriteLine($"检测到完整响应: {response.ToString().Trim()}");
                                    return response.ToString().Trim();
                                }
                            }
                            // 如果已经收到数据，检查是否应该继续等待
                            else if (hasReceivedData)
                            {
                                // 如果距离上次收到数据超过200ms，认为传输结束
                                if ((DateTime.Now - lastDataTime).TotalMilliseconds > 200)
                                {
                                    string finalResponse = response.ToString().Trim();
                                    if (!string.IsNullOrEmpty(finalResponse))
                                    {
                                        Debug.WriteLine($"数据传输结束，返回: {finalResponse}");
                                        return finalResponse;
                                    }
                                }
                            }
                        }
                        Thread.Sleep(10); // 避免CPU占用过高
                    }

                    // 超时后返回已收到的数据
                    string timeoutResponse = response.ToString().Trim();
                    Debug.WriteLine($"接收超时，返回: {timeoutResponse}");
                    return timeoutResponse;
                }
                catch (Exception ex)
                {
                    OnErrorOccurred(ex);
                    return string.Empty;
                }
            });
        }

        //清空缓冲区
        public void ClearBuffer()
        {
            if (IsConnected)
            {
                try
                {
                    lock (_lockObject)
                    {
                        // 清空串口缓冲区
                        _serialPort.DiscardInBuffer();
                        _serialPort.DiscardOutBuffer();

                        // 清空内部数据缓冲区
                        _dataBuffer.Clear();

                    }
                }
                catch (Exception ex)
                {
                    OnErrorOccurred(ex);
                }
            }
        }

        // 触发数据接收事件
        protected virtual void OnDataReceived(string data)
        {
            DataReceived?.Invoke(this, data);
        }

        // 触发错误事件
        protected virtual void OnErrorOccurred(Exception ex)
        {
            ErrorOccurred?.Invoke(this, ex);
        }
        public int GetBytesToRead()
        {
            return IsConnected ? _serialPort.BytesToRead : 0;
        }

        public string ReadExisting()
        {
            return IsConnected ? _serialPort.ReadExisting() : string.Empty;
        }

    }
}
