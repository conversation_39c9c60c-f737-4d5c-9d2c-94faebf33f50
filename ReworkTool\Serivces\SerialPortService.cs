using ReworkTool.Interfaces;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace ReworkTool.Serivces
{
    public class SerialPortService:ISerialPortService
    {
        private SerialPort _serialPort;
        private readonly object _lockObject = new object();
        private StringBuilder _dataBuffer = new StringBuilder();

        public bool IsConnected => _serialPort != null && _serialPort.IsOpen;
        public string CurrentPort => IsConnected ? _serialPort.PortName : string.Empty;

        public event EventHandler<string> DataReceived;
        public event EventHandler<Exception> ErrorOccurred;

        //连接串口
        public async Task<bool> ConnectAsync(string portName, int baudRate = 9600)
        {
            return await Task.Run(() =>
            {
                try
                {
                    // 强制断开现有连接(处理设备被拔出的情况)
                    if (_serialPort != null)
                    {
                        try { _serialPort.DataReceived -= SerialPort_DataReceived; } catch { }
                        try { if (_serialPort.IsOpen) _serialPort.Close(); } catch { }
                        try { _serialPort.Dispose(); } catch { }
                        _serialPort = null;
                        GC.Collect();
                        Thread.Sleep(300); // 等待系统释放
                    }
                    // 如果已经连接到相同的端口，直接返回成功
                    if (IsConnected && _serialPort.PortName == portName)
                    {
                        return true;
                    }

                    // 如果已经连接到其他端口，先断开
                    if (IsConnected)
                    {
                        _serialPort.DataReceived -= SerialPort_DataReceived;
                        _serialPort.Close();
                        _serialPort.Dispose();
                        _serialPort = null;
                    }

                    // 创建新的串口连接
                    _serialPort = new SerialPort
                    {
                        PortName = portName,
                        BaudRate = baudRate,
                        DataBits = 8,
                        Parity = Parity.None,
                        StopBits = StopBits.One,
                        ReadTimeout = 1000,
                        WriteTimeout = 1000
                    };

                    // 清空缓冲区
                    _dataBuffer.Clear();

                    // 打开串口
                    _serialPort.Open();

                    // 注册数据接收事件
                    _serialPort.DataReceived += SerialPort_DataReceived;
                    return true;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"连接异常详情: {ex.GetType().Name}");
                    Debug.WriteLine($"异常消息: {ex.Message}");
                    if (ex.InnerException != null)
                    {
                        Debug.WriteLine($"内部异常: {ex.InnerException.Message}");
                    }
                    Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                    return false;
                }
            });
        }

        //断开串口连接
        public Task<bool> DisconnectAsync()
        {
            return Task.Run(() =>
            {
                try
                {
                    if (_serialPort != null)
                    {
                        // 取消事件订阅 - 即使设备被拔出也要尝试
                        try { _serialPort.DataReceived -= SerialPort_DataReceived; } catch { }

                        // 尝试关闭 - 设备被拔出时可能失败
                        try
                        {
                            if (_serialPort.IsOpen) _serialPort.Close();
                            Debug.WriteLine("串口已关闭");
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"关闭串口失败(可能被拔出): {ex.Message}");
                        }

                        // 尝试释放 - 设备被拔出时可能失败
                        try
                        {
                            _serialPort.Dispose();
                            Debug.WriteLine("串口已释放");
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"释放串口失败(可能被拔出): {ex.Message}");
                        }
                    }

                    return true;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"断开异常: {ex.Message}");
                    return true; // 返回true，因为我们已经尽力清理了
                }
                finally
                {
                    // 无论如何都要清理引用
                    _serialPort = null;
                    _dataBuffer.Clear();

                    // 强制垃圾回收，处理设备被拔出的情况
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    Thread.Sleep(200);

                    Debug.WriteLine("串口对象已清空");
                }
            });
        }

        //发送命令
        public Task<bool> SendCommandAsync(string command)
        {
            return Task.Run(() =>
            {
                if (!IsConnected)
                {
                    OnErrorOccurred(new InvalidOperationException("串口未连接"));
                    return false;
                }

                try
                {
                    lock (_lockObject)
                    {
                        // 清空内部数据缓冲区，为新的响应做准备
                        _dataBuffer.Clear();

                        // 发送命令
                        _serialPort.Write(command);
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    OnErrorOccurred(ex);
                    return false;
                }
            });
        }

        //获取串口返回值
        public Task<string> ReceiveDataAsync(int timeout = 1000)
        {
            return Task.Run(() =>
            {
                if (!IsConnected) return string.Empty;

                try
                {
                    StringBuilder response = new StringBuilder();
                    DateTime startTime = DateTime.Now;
                    bool hasReceivedData = false;

                    while ((DateTime.Now - startTime).TotalMilliseconds < timeout)
                    {
                        lock (_lockObject)
                        {
                            // 检查是否有新数据到达
                            if (_serialPort.BytesToRead > 0)
                            {
                                string data = _serialPort.ReadExisting();
                                response.Append(data);
                                hasReceivedData = true;

                                // 检查是否收到完整响应
                                string current = response.ToString().ToUpper();
                                if (current.Contains("OK") || current.Contains("ERROR"))
                                {
                                    return response.ToString().Trim();
                                }
                            }
                            // 如果已经收到数据但没有OK/ERROR，继续等待一小段时间
                            else if (hasReceivedData)
                            {
                                // 给设备一点时间发送完整响应
                                Thread.Sleep(50);
                                // 再次检查
                                if (_serialPort.BytesToRead > 0)
                                {
                                    string additionalData = _serialPort.ReadExisting();
                                    response.Append(additionalData);
                                    string current = response.ToString().ToUpper();
                                    if (current.Contains("OK") || current.Contains("ERROR"))
                                    {
                                        return response.ToString().Trim();
                                    }
                                }
                                // 如果没有更多数据，返回已收到的数据
                                else if (response.Length > 0)
                                {
                                    return response.ToString().Trim();
                                }
                            }
                        }
                        Thread.Sleep(10); // 避免CPU占用过高
                    }

                    // 超时后返回已收到的数据
                    return response.ToString().Trim();
                }
                catch (Exception ex)
                {
                    OnErrorOccurred(ex);
                    return string.Empty;
                }
            });
        }

        // 串口数据接收事件处理监听事件
        // 注意：此事件主要用于实时监控，不应与ReceiveDataAsync同时使用
        private void SerialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            try
            {
                if (!IsConnected) return;

                // 这个事件处理器主要用于调试和监控
                // 实际的数据读取由ReceiveDataAsync方法处理
                // 避免在这里读取数据，防止与ReceiveDataAsync冲突

                // 只是触发事件通知有数据到达，但不读取数据
                // OnDataReceived("数据到达");
            }
            catch (Exception ex)
            {
                OnErrorOccurred(ex);
            }
        }
        //清空缓冲区
        public void ClearBuffer()
        {
            if (IsConnected)
            {
                try
                {
                    lock (_lockObject)
                    {
                        // 清空串口缓冲区
                        _serialPort.DiscardInBuffer();
                        _serialPort.DiscardOutBuffer();

                        // 清空内部数据缓冲区
                        _dataBuffer.Clear();

                    }
                }
                catch (Exception ex)
                {
                    OnErrorOccurred(ex);
                }
            }
        }

        // 触发数据接收事件
        protected virtual void OnDataReceived(string data)
        {
            DataReceived?.Invoke(this, data);
        }

        // 触发错误事件
        protected virtual void OnErrorOccurred(Exception ex)
        {
            ErrorOccurred?.Invoke(this, ex);
        }
        public int GetBytesToRead()
        {
            return IsConnected ? _serialPort.BytesToRead : 0;
        }

        public string ReadExisting()
        {
            return IsConnected ? _serialPort.ReadExisting() : string.Empty;
        }

    }
}
