﻿using Dapper;
using MySql.Data.MySqlClient;
using ReworkTool.Interfaces;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace ReworkTool.Serivces
{
    public class DatabaseService : IDatabaseService
    {
        private readonly string _connectionString = "Server=rm-wz94ucg0gjtlor05jvo.mysql.rds.aliyuncs.com;Database=xiaxin_project;Uid=XiaXin_project_plant;Pwd=************$Ju@;";
        public async Task CreateTable(string tableName, List<string> fieldNames)
        {
            try
            {
                using var connection = new MySqlConnection(_connectionString);
                //检测表是否存在
                var tableExists = connection.QuerySingleOrDefault<int>(
                   "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = @tableName",
                   new { tableName }) > 0;

                if (!tableExists)
                {
                    var columns = "id INT AUTO_INCREMENT PRIMARY KEY, created_time DATETIME DEFAULT CURRENT_TIMESTAMP";
                    foreach (var fieldName in fieldNames.Where(f => !string.IsNullOrEmpty(f)))
                    {
                        columns += $", `{fieldName}` VARCHAR(255)";
                    }
                    var sql = $"CREATE TABLE `{tableName}` ({columns})";
                    await connection.ExecuteAsync($"CREATE TABLE `{tableName}` ({columns})");
                }
                else
                {
                    // 检查并添加缺失的列
                    foreach (var fieldName in fieldNames.Where(f => !string.IsNullOrEmpty(f)))
                    {
                        var columnExists = connection.QueryFirstOrDefault<int>(
                            "SELECT COUNT(*) FROM information_schema.columns WHERE table_name = @tableName AND column_name = @fieldName",
                            new { tableName, fieldName }) > 0;

                        if (!columnExists)
                        {
                            await connection.ExecuteAsync($"ALTER TABLE `{tableName}` ADD COLUMN `{fieldName}` VARCHAR(255)");
                        }
                    }
                }
            }catch(Exception ex)
            {
                MessageBox.Show($"数据库操作失败: {ex.Message}");
            }
        }

        public async Task SaveRecord(string tableName, Dictionary<string, object> fieldValues)
        {
            using var connection = new MySqlConnection(_connectionString);
            var fields = string.Join(",", fieldValues.Keys.Select(k => $"`{k}`"));
            var values = string.Join(",", fieldValues.Keys.Select(k => $"@{k}"));
            await connection.ExecuteAsync($"INSERT INTO `{tableName}` ({fields}) VALUES ({values})", fieldValues);
        }
    }
}
