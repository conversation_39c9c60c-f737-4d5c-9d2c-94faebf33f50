﻿using Prism.Mvvm;
using ReworkTool.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ReworkTool
{
    public class AppConfig:BindableBase
    {
        public string MysqlTable { get; set; }
        public List<string> DeviceList { get; set; }
        public ObservableCollection<CommandConfig> CommandList { get; set; }

    }
}
