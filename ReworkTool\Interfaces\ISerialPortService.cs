﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ReworkTool.Interfaces
{
    public interface ISerialPortService
    {
        //连接和断开串口
        Task<bool> ConnectAsync(string portName, int baudRate = 9600);
        Task<bool> DisconnectAsync();
        //发送和接收数据
        Task<bool> SendCommandAsync(string command);
        Task<string> ReceiveDataAsync(int timeout = 1000);
        //清空缓冲区
        void ClearBuffer();
        //状态属性
        bool IsConnected { get; }
        string CurrentPort { get; }
        //事件处理
        event EventHandler<string> DataReceived;
        event EventHandler<Exception> ErrorOccurred;
        string ReadExisting();
        int GetBytesToRead();
    }
}
