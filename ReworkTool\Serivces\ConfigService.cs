﻿using ReworkTool.Interfaces;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace ReworkTool.Serivces
{
    public class ConfigService : IConfigService
    {
        public ObservableCollection<AppConfig> Configurations =>new ObservableCollection<AppConfig>();

        public void LoadConfigsFromFile(string filePath)
        {
            try
            {

            }
            catch (Exception ex) 
            {
                MessageBox.Show($"加载配置文件失败{ex.Message}");
                return;
            }
        }
    }
}
