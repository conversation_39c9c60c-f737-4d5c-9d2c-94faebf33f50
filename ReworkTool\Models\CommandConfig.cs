﻿using Prism.Mvvm;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ReworkTool.Models
{
    public class CommandConfig:BindableBase
    {
        public string Command { get; set; }
        public string RegularExpression { get; set; }
        public bool ReturnValueRequired { get; set; }
        public object ExpectReturnValue { get; set; }
        public string FieldName { get; set; }
        public int NumberOfTests { get; set; }
        public int WaitTime { get; set; }

        private string _status ;
        public string Status
        {
            get { return _status; }
            set { SetProperty(ref _status, value); }
        }

        private string _returnValue;
        public string ReturnValue
        {
            get { return _returnValue; }
            set { SetProperty(ref _returnValue, value); }
        }
        
        private bool _isSuccess;
        public bool IsSuccess
        {
            get { return _isSuccess; }
            set { SetProperty(ref _isSuccess, value); }
        }
    }
}
