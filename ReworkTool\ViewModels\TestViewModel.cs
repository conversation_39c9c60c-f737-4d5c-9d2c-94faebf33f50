using Prism.Mvvm;
using Prism.Regions;
using ReworkTool.Interfaces;
using ReworkTool.Models;
using ReworkTool.Serivces;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;

namespace ReworkTool.ViewModels
{
    public class TestViewModel : BindableBase, INavigationAware
    {
        private string _mysqlTable;
        private string _deviceCom;
        private string _status;
        private ObservableCollection<CommandConfig> _commandList;
        private ISerialPortService _serialPortService;
        private readonly IDatabaseService _databaseService;
        private CancellationTokenSource _cancellationTokenSource;

        public TestViewModel(IDatabaseService databaseService)
        {
            _databaseService = databaseService;
        }

        public string MysqlTable
        {
            get { return _mysqlTable; }
            set { SetProperty(ref _mysqlTable, value); }
        }
        public string DeviceCom
        {
            get { return _deviceCom; }
            set{SetProperty(ref _deviceCom, value);}
        }
        public ObservableCollection<CommandConfig> CommandList
        {
            get { return _commandList; }
            set { SetProperty(ref _commandList, value); }
        }
        public string Status
        {
            get { return _status; }
            set { SetProperty(ref _status, value); }
        }
        public int ViewHashCode
        {
            get { return this.GetHashCode(); }

        }
        public bool IsNavigationTarget(NavigationContext navigationContext)
        {
            return false;
        }

        public void OnNavigatedFrom(NavigationContext navigationContext)
        {
            
        }
        private async void StartAutoTesting()
        {
            _cancellationTokenSource = new CancellationTokenSource();
            try
            {
                while (!_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    Status = "等待设备连接...";
                    if (SerialPort.GetPortNames().Contains(DeviceCom))
                    {
                        _serialPortService = new SerialPortService();
                        var valueDic = new Dictionary<string, object>();
                        if (await _serialPortService.ConnectAsync(DeviceCom))
                        {
                            Status = "开始测试...";
                            bool allTestsPassed = true;

                            // 初始化命令状态
                            foreach (var cmd in CommandList)
                            {
                                cmd.Status = "等待测试";
                                cmd.ReturnValue = string.Empty;
                            }

                            // 执行命令列表
                            foreach (var cmd in CommandList)
                            {
                                bool cmdSuccess = false;
                                for (int i = 0; i < cmd.NumberOfTests; i++)
                                {
                                    if (await _serialPortService.SendCommandAsync(cmd.Command + "\r\n"))
                                    {
                                        // 等待设备响应
                                        await Task.Delay(100);

                                        // 使用统一的数据接收方法
                                        cmd.ReturnValue = await _serialPortService.ReceiveDataAsync(2000);
                                        Debug.WriteLine($"发送:{cmd.Command} 接收:{cmd.ReturnValue}");

                                        if (cmd.ReturnValueRequired)
                                        {
                                            cmdSuccess = ValidateCommandResult(cmd, valueDic);
                                        }
                                        else
                                        {
                                            // 如果不需要验证返回值，只要有响应就算成功
                                            cmdSuccess = !string.IsNullOrEmpty(cmd.ReturnValue);
                                            break;
                                        }
                                        if (cmdSuccess) break;
                                    }

                                }
                                cmd.Status = cmdSuccess ? "成功" : "失败";
                                // 命令失败则跳出
                                if (!cmdSuccess)
                                {
                                    allTestsPassed = false;
                                    Status = $"测试失败: {cmd.Command}";
                                    break;
                                }
                                if (allTestsPassed)
                                {
                                    Status = "所有测试通过";
                                    await SaveTestResults(valueDic);
                                }
                                await Task.Delay(cmd.WaitTime, _cancellationTokenSource.Token);
                            }
                            foreach (var item in valueDic)
                            {
                                Debug.WriteLine($"key:{item.Key}-value:{item.Value}");
                            }
                            await _serialPortService.DisconnectAsync();
                        }
                        _serialPortService = null;
                    }

                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine(ex);
            }
            //await Task.Delay(1000); // 每秒检测一次

        }
        private bool ValidateCommandResult(CommandConfig cmd, Dictionary<string, object> valueDic)
        {
            if (!cmd.ReturnValueRequired)
                return true;

            string clearData = Regex.Replace(cmd.ReturnValue, @"\s", "");
            if (!string.IsNullOrEmpty(cmd.RegularExpression))
            {
                var match = Regex.Match(clearData, cmd.RegularExpression);
                if (match.Success && !string.IsNullOrEmpty(cmd.FieldName))
                {
                    if(cmd.ExpectReturnValue is long longExpectReturnValue)
                    {
                        if (long.TryParse(match.Groups[1].Value, out long actualValue))
                        {
                            if (actualValue > longExpectReturnValue&&actualValue!=99&actualValue!=255)
                            {
                                Debug.WriteLine($"实际值 {actualValue} > 期望值 {longExpectReturnValue}");
                                valueDic[cmd.FieldName] = actualValue;
                                return true;
                            }
                            else
                            {
                                Debug.WriteLine($"不满足：{actualValue}>{longExpectReturnValue}且!=255 !=99");
                                return false;
                            }
                        }
                    }
                    if(cmd.ExpectReturnValue is string StringExpectReturnValue)
                    {
                        if (match.Groups[0].Value.Contains(StringExpectReturnValue))
                        {
                            Debug.WriteLine($"实际值 {match.Groups[1].Value}包含 期望值 {StringExpectReturnValue}");
                            valueDic[cmd.FieldName] = match.Groups[1].Value;
                            return true;
                        }
                        else
                        {
                            Debug.WriteLine($"实际值 {match.Groups[1].Value}不包含期望值 {StringExpectReturnValue}");
                            return false;
                        }
                    }
                }
            }
            return false;
        }

        private void ResetRemainingCommands(CommandConfig failedCommand)
        {
            bool foundFailed = false;
            foreach (var cmd in CommandList)
            {
                if (cmd == failedCommand) foundFailed = true;
                else if (foundFailed)
                {
                    cmd.Status = "未测试";
                    cmd.ReturnValue = string.Empty;
                }
            }
        }
        private async Task SaveTestResults(Dictionary<string, object> valueDic)
        {
            if (valueDic.Count > 0 && !string.IsNullOrEmpty(MysqlTable))
            {
                await _databaseService.SaveRecord(MysqlTable, valueDic);
            }
        }
        public void OnNavigatedTo(NavigationContext navigationContext)
        {
            var parameterMap = new Dictionary<string, Action<object>>
            {
                ["MysqlTable"] = value => MysqlTable = value?.ToString(),
                ["Device"] = value => DeviceCom = value?.ToString(),
                ["CommandList"] = value => CommandList = value as ObservableCollection<CommandConfig>
            };
            foreach(var kvp in parameterMap)
            {
                if (navigationContext.Parameters.ContainsKey(kvp.Key))
                {
                    var parameterValue = navigationContext.Parameters[kvp.Key];
                    if (parameterValue != null)
                    {
                        kvp.Value(parameterValue);
                    }
                }
            }

            var mainWindow = Application.Current.MainWindow;
            mainWindow.WindowState = WindowState.Maximized;
            StartAutoTesting();
        }
    }
}
