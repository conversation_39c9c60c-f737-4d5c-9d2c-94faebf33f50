﻿<UserControl x:Class="ReworkTool.Views.TestView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:ReworkTool.Views"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <TextBlock Grid.Row="0" Text="{Binding ViewHashCode}" Margin="5"/>
        <TextBlock Grid.Row="1" Text="{Binding Status}" FontSize="16" FontWeight="Bold" Margin="5" HorizontalAlignment="Center"/>
        
        <DataGrid Grid.Row="2" Style="{StaticResource DataGridBaseStyle}" ItemsSource="{Binding CommandList}" AutoGenerateColumns="False" CanUserAddRows="False" CanUserReorderColumns="False" CanUserSortColumns="False" IsReadOnly="True">
            <DataGrid.RowStyle>
                <Style TargetType="DataGridRow">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding Status}" Value="测试中...">
                            <Setter Property="Background" Value="LightYellow"/>
                            <Setter Property="Foreground" Value="Black"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding Status}" Value="成功">
                            <Setter Property="Background" Value="LightGreen"/>
                            <Setter Property="Foreground" Value="Black"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding Status}" Value="失败">
                            <Setter Property="Background" Value="LightCoral"/>
                            <Setter Property="Foreground" Value="Black"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding Status}" Value="等待测试">
                            <Setter Property="Background" Value="LightGray"/>
                            <Setter Property="Foreground" Value="Black"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding Status}" Value="未测试">
                            <Setter Property="Background" Value="White"/>
                            <Setter Property="Foreground" Value="Black"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </DataGrid.RowStyle>
            <DataGrid.Columns>
                <DataGridTextColumn Header="执行指令" Binding="{Binding Command}"/>
                <DataGridTextColumn Header="正则表达" Binding="{Binding RegularExpression}"/>
                <DataGridTextColumn Header="是否需要返回值" Binding="{Binding ReturnValueRequired}"/>
                <DataGridTextColumn Header="期待返回值" Binding="{Binding ExpectReturnValue}"/>
                <DataGridTextColumn Header="保存的字段" Binding="{Binding FieldName}"/>
                <DataGridTextColumn Header="单个指令循环测试的次数" Binding="{Binding NumberOfTests}"/>
                <DataGridTextColumn Header="指令执行完后等待的时间(ms)" Binding="{Binding WaitTime}"/>
                <DataGridTextColumn Header="收到的返回值" Binding="{Binding ReturnValue}"/>
                <DataGridTextColumn Header="状态" Binding="{Binding Status}"/>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</UserControl>
