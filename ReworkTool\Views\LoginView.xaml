﻿<UserControl x:Class="ReworkTool.Views.LoginView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:ReworkTool.Views"
             xmlns:converters="clr-namespace:ReworkTool.Converters"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
            <Button  Content="📁 选择配置文件文件（JSON）" 
                 Command="{Binding SelectFileCommand}"
                 Width="200" Height="35" Margin="5,10" />
        </StackPanel>
        <TextBlock Grid.Row="1" 
            Text="{Binding SelectedFileName, StringFormat='✅ 文件：{0}'}"
            Visibility="{Binding IsFileSelected, Converter={StaticResource BooleanToVisibilityConverter}}"
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            Margin="0,5,0,10"/>
        <TextBlock Grid.Row="1" 
            Text="⚠️ 请先选择一个配置文件"
            Foreground="Red"
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            Visibility="{Binding IsFileSelected, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Inverted}"
            Margin="0,5,0,10"/>
        <Button Grid.Row="2" Style="{StaticResource ButtonPrimary}" Content="确定" Command="{Binding ImportFileCommand}" Margin="5,10"/>
    </Grid>
</UserControl>
